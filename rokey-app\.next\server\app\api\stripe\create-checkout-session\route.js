/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/stripe/create-checkout-session/route";
exports.ids = ["app/api/stripe/create-checkout-session/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute&page=%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute&page=%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_stripe_create_checkout_session_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/stripe/create-checkout-session/route.ts */ \"(rsc)/./src/app/api/stripe/create-checkout-session/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/stripe/create-checkout-session/route\",\n        pathname: \"/api/stripe/create-checkout-session\",\n        filename: \"route\",\n        bundlePath: \"app/api/stripe/create-checkout-session/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\stripe\\\\create-checkout-session\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_stripe_create_checkout_session_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute&page=%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/stripe/create-checkout-session/route.ts":
/*!*************************************************************!*\
  !*** ./src/app/api/stripe/create-checkout-session/route.ts ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var stripe__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! stripe */ \"(rsc)/./node_modules/stripe/esm/stripe.esm.node.js\");\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var _lib_stripe_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/stripe-config */ \"(rsc)/./src/lib/stripe-config.ts\");\n\n\n\n\nconst stripe = new stripe__WEBPACK_IMPORTED_MODULE_1__[\"default\"](_lib_stripe_config__WEBPACK_IMPORTED_MODULE_2__.STRIPE_KEYS.secretKey, {\n    apiVersion: '2025-02-24.acacia'\n});\nconst supabase = (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_3__.createClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY);\nasync function POST(req) {\n    try {\n        const { priceId, userId, userEmail, tier, signup, pendingUserData } = await req.json();\n        console.log('Create checkout session request:', {\n            priceId,\n            tier,\n            userId: userId ? `${userId.substring(0, 8)}...` : 'none',\n            userEmail: userEmail ? `${userEmail.substring(0, 3)}***` : 'none',\n            signup,\n            hasPendingUserData: !!pendingUserData\n        });\n        // For signup flow, we don't need userId yet\n        if (signup) {\n            // Validate required fields for signup\n            if (!priceId || !userEmail || !tier || !pendingUserData) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Missing required fields for signup: priceId, userEmail, tier, pendingUserData'\n                }, {\n                    status: 400\n                });\n            }\n        } else {\n            // Validate required fields for existing user\n            if (!priceId || !userId || !userEmail || !tier) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Missing required fields: priceId, userId, userEmail, tier'\n                }, {\n                    status: 400\n                });\n            }\n        }\n        // Validate tier\n        if (![\n            'starter',\n            'professional',\n            'enterprise'\n        ].includes(tier)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid tier. Must be starter, professional, or enterprise'\n            }, {\n                status: 400\n            });\n        }\n        // Validate price ID matches tier\n        const expectedPriceId = getPriceIdForTier(tier);\n        if (priceId !== expectedPriceId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Price ID does not match selected tier'\n            }, {\n                status: 400\n            });\n        }\n        // Check if user already has an active subscription (only for existing users)\n        // Skip this check for users with pending payment status\n        if (!signup && userId) {\n            // First check if this is a pending payment user\n            const { data: authUser } = await supabase.auth.admin.getUserById(userId);\n            const paymentStatus = authUser?.user?.user_metadata?.payment_status;\n            // If user has pending payment status, treat them as signup flow\n            if (paymentStatus === 'pending') {\n                console.log('User has pending payment status, allowing checkout to proceed');\n            } else {\n                // Only check for existing subscription if not a pending payment user\n                const { data: existingSubscription } = await supabase.from('subscriptions').select('*').eq('user_id', userId).eq('status', 'active').single();\n                if (existingSubscription) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: 'User already has an active subscription'\n                    }, {\n                        status: 400\n                    });\n                }\n            }\n        }\n        // Create or retrieve Stripe customer\n        let customer;\n        // First, try to find existing customer by email\n        const existingCustomers = await stripe.customers.list({\n            email: userEmail,\n            limit: 1\n        });\n        if (existingCustomers.data.length > 0) {\n            customer = existingCustomers.data[0];\n        } else {\n            // Create new customer\n            customer = await stripe.customers.create({\n                email: userEmail,\n                metadata: {\n                    user_id: userId\n                }\n            });\n        }\n        // Create checkout session\n        const sessionParams = {\n            customer: customer.id,\n            payment_method_types: [\n                'card'\n            ],\n            line_items: [\n                {\n                    price: priceId,\n                    quantity: 1\n                }\n            ],\n            mode: 'subscription',\n            success_url: `${ true ? 'http://localhost:3000' : 0}/api/stripe/payment-success?session_id={CHECKOUT_SESSION_ID}&plan=${tier}`,\n            cancel_url: `${ true ? 'http://localhost:3000' : 0}/pricing?plan=${tier}&payment_cancelled=true`,\n            metadata: {\n                user_id: userId || 'pending_signup',\n                tier: tier,\n                signup: signup ? 'true' : 'false',\n                pending_user_data: signup ? JSON.stringify(pendingUserData) : undefined\n            },\n            subscription_data: {\n                metadata: {\n                    user_id: userId || 'pending_signup',\n                    tier: tier,\n                    signup: signup ? 'true' : 'false',\n                    pending_user_data: signup ? JSON.stringify(pendingUserData) : undefined\n                }\n            },\n            allow_promotion_codes: true,\n            billing_address_collection: 'required',\n            customer_update: {\n                address: 'auto',\n                name: 'auto'\n            }\n        };\n        const session = await stripe.checkout.sessions.create(sessionParams);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            sessionId: session.id,\n            url: session.url\n        });\n    } catch (error) {\n        console.error('Error creating checkout session:', error);\n        if (error instanceof stripe__WEBPACK_IMPORTED_MODULE_1__[\"default\"].errors.StripeError) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `Stripe error: ${error.message}`\n            }, {\n                status: 400\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\nfunction getPriceIdForTier(tier) {\n    switch(tier){\n        case 'free':\n            return _lib_stripe_config__WEBPACK_IMPORTED_MODULE_2__.STRIPE_PRICE_IDS.FREE;\n        case 'starter':\n            return _lib_stripe_config__WEBPACK_IMPORTED_MODULE_2__.STRIPE_PRICE_IDS.STARTER;\n        case 'professional':\n            return _lib_stripe_config__WEBPACK_IMPORTED_MODULE_2__.STRIPE_PRICE_IDS.PROFESSIONAL;\n        case 'enterprise':\n            return _lib_stripe_config__WEBPACK_IMPORTED_MODULE_2__.STRIPE_PRICE_IDS.ENTERPRISE;\n        default:\n            throw new Error(`Invalid tier: ${tier}`);\n    }\n}\n// Helper function to get tier display names\nfunction getTierDisplayName(tier) {\n    switch(tier){\n        case 'free':\n            return 'Free';\n        case 'starter':\n            return 'Starter';\n        case 'professional':\n            return 'Professional';\n        case 'enterprise':\n            return 'Enterprise';\n        default:\n            return 'Unknown';\n    }\n}\n// Helper function to get tier prices\nfunction getTierPrice(tier) {\n    switch(tier){\n        case 'free':\n            return '$0';\n        case 'starter':\n            return '$19';\n        case 'professional':\n            return '$49';\n        case 'enterprise':\n            return '$149';\n        default:\n            return 'Unknown';\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/stripe/create-checkout-session/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/stripe-config.ts":
/*!**********************************!*\
  !*** ./src/lib/stripe-config.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PUBLIC_STRIPE_PRICE_IDS: () => (/* binding */ PUBLIC_STRIPE_PRICE_IDS),\n/* harmony export */   STRIPE_ENV_INFO: () => (/* binding */ STRIPE_ENV_INFO),\n/* harmony export */   STRIPE_KEYS: () => (/* binding */ STRIPE_KEYS),\n/* harmony export */   STRIPE_PRICE_IDS: () => (/* binding */ STRIPE_PRICE_IDS),\n/* harmony export */   STRIPE_PRODUCT_IDS: () => (/* binding */ STRIPE_PRODUCT_IDS)\n/* harmony export */ });\n// Stripe Configuration with Environment Detection\n// Automatically switches between test and live keys based on environment\nconst isProduction = \"development\" === 'production';\n// Stripe Keys - Auto-selected based on environment\nconst STRIPE_KEYS = {\n    publishableKey: isProduction ? process.env.STRIPE_LIVE_PUBLISHABLE_KEY : process.env.STRIPE_TEST_PUBLISHABLE_KEY,\n    secretKey: isProduction ? process.env.STRIPE_LIVE_SECRET_KEY : process.env.STRIPE_TEST_SECRET_KEY,\n    webhookSecret: isProduction ? process.env.STRIPE_LIVE_WEBHOOK_SECRET : process.env.STRIPE_TEST_WEBHOOK_SECRET\n};\n// Stripe Price IDs - Auto-selected based on environment\nconst STRIPE_PRICE_IDS = {\n    FREE: isProduction ? process.env.STRIPE_LIVE_FREE_PRICE_ID : process.env.STRIPE_TEST_FREE_PRICE_ID,\n    STARTER: isProduction ? process.env.STRIPE_LIVE_STARTER_PRICE_ID : process.env.STRIPE_TEST_STARTER_PRICE_ID,\n    PROFESSIONAL: isProduction ? process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID : process.env.STRIPE_TEST_PROFESSIONAL_PRICE_ID,\n    ENTERPRISE: isProduction ? process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID : process.env.STRIPE_TEST_ENTERPRISE_PRICE_ID\n};\n// Stripe Product IDs - Auto-selected based on environment\nconst STRIPE_PRODUCT_IDS = {\n    FREE: isProduction ? process.env.STRIPE_LIVE_FREE_PRODUCT_ID : process.env.STRIPE_TEST_FREE_PRODUCT_ID,\n    STARTER: isProduction ? process.env.STRIPE_LIVE_STARTER_PRODUCT_ID : process.env.STRIPE_TEST_STARTER_PRODUCT_ID,\n    PROFESSIONAL: isProduction ? process.env.STRIPE_LIVE_PROFESSIONAL_PRODUCT_ID : process.env.STRIPE_TEST_PROFESSIONAL_PRODUCT_ID,\n    ENTERPRISE: isProduction ? process.env.STRIPE_LIVE_ENTERPRISE_PRODUCT_ID : process.env.STRIPE_TEST_ENTERPRISE_PRODUCT_ID\n};\n// Public Price IDs for frontend (auto-selected based on environment)\nconst PUBLIC_STRIPE_PRICE_IDS = {\n    FREE: isProduction ? process.env.STRIPE_LIVE_FREE_PRICE_ID : process.env.STRIPE_TEST_FREE_PRICE_ID,\n    STARTER: isProduction ? process.env.STRIPE_LIVE_STARTER_PRICE_ID : process.env.STRIPE_TEST_STARTER_PRICE_ID,\n    PROFESSIONAL: isProduction ? process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID : process.env.STRIPE_TEST_PROFESSIONAL_PRICE_ID,\n    ENTERPRISE: isProduction ? process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID : process.env.STRIPE_TEST_ENTERPRISE_PRICE_ID\n};\n// Environment info for debugging\nconst STRIPE_ENV_INFO = {\n    isProduction,\n    environment: isProduction ? 'LIVE' : 'TEST',\n    keysUsed: {\n        publishable: STRIPE_KEYS.publishableKey ? STRIPE_KEYS.publishableKey.substring(0, 20) + '...' : 'undefined',\n        secret: STRIPE_KEYS.secretKey ? STRIPE_KEYS.secretKey.substring(0, 20) + '...' : 'undefined',\n        webhook: STRIPE_KEYS.webhookSecret ? STRIPE_KEYS.webhookSecret.substring(0, 15) + '...' : 'undefined'\n    }\n};\n// Log environment info in development\nif (!isProduction) {\n    console.log('🔧 Stripe Environment:', STRIPE_ENV_INFO.environment);\n    console.log('🔑 Using keys:', STRIPE_ENV_INFO.keysUsed);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/stripe-config.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/stripe","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/qs","vendor-chunks/call-bind-apply-helpers","vendor-chunks/get-proto","vendor-chunks/object-inspect","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/side-channel","vendor-chunks/side-channel-weakmap","vendor-chunks/side-channel-map","vendor-chunks/side-channel-list","vendor-chunks/hasown","vendor-chunks/get-intrinsic","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/call-bound"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute&page=%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fcreate-checkout-session%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();